import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 职级创建弹窗
class GradeDialog {
  /// 显示职级创建弹窗
  static Future<void> show({
    required BuildContext context,
    void Function()? onSuccess,
    void Function()? onError,
  }) {
    return AppDialog.show(
      context: context,
      width: 480,
      title: '添加职级',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      child: _GradeDialogContent(onSuccess: onSuccess, onError: onError),
    );
  }
}

/// 职级创建弹窗内容组件
class _GradeDialogContent extends StatefulWidget {
  /// 成功回调
  final void Function()? onSuccess;

  /// 失败回调
  final void Function()? onError;

  const _GradeDialogContent({this.onSuccess, this.onError});

  @override
  State<_GradeDialogContent> createState() => _GradeDialogContentState();
}

class _GradeDialogContentState extends State<_GradeDialogContent> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    _nameController.clear();
    setState(() {
      isAddNext = false;
    });
  }

  /// 提交职级创建请求
  Future<void> createRequest(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      btnLoading = true;
    });

    try {
      // 模拟API调用延迟
      await Future.delayed(const Duration(seconds: 1));

      ToastManager.success('职级创建成功');
      widget.onSuccess?.call();
      resetFormData();

      if (!isAddNext && mounted) {
        Navigator.of(context).pop();
      }
    } catch (error) {
      ToastManager.error('创建失败: $error');
      widget.onError?.call();
    } finally {
      if (mounted) {
        setState(() {
          btnLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 表单内容
        Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppInput(
                label: "职级名称",
                labelWidth: 80,
                labelPosition: LabelPosition.left,
                hintText: "请输入职级名称",
                size: InputSize.medium,
                controller: _nameController,
                maxLength: 30,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入职级名称';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        // 底部操作区域
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Checkbox(
              value: isAddNext,
              onChanged: (value) {
                setState(() {
                  isAddNext = value ?? false;
                });
              },
            ),
            const Text('继续新建下一条'),
            const SizedBox(width: 10),
            AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
            const SizedBox(width: 10),
            AppButton(
              text: '确定',
              type: ButtonType.primary,
              loading: btnLoading,
              onPressed: () => createRequest(context),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
}
