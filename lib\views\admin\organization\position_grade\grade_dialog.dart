import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 职级创建弹窗
class GradeDialog extends StatefulWidget {
  /// 成功回调
  final void Function()? onSuccess;

  /// 失败回调
  final void Function()? onError;

  const GradeDialog({super.key, this.onSuccess, this.onError});

  /// 显示职级创建弹窗
  static Future<void> show({
    required BuildContext context,
    void Function()? onSuccess,
    void Function()? onError,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => GradeDialog(onSuccess: onSuccess, onError: onError),
    );
  }

  @override
  State<GradeDialog> createState() => _GradeDialogState();
}

class _GradeDialogState extends State<GradeDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    _nameController.clear();
    setState(() {
      isAddNext = false;
    });
  }

  /// 提交职级创建请求
  Future<void> createRequest(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      btnLoading = true;
    });

    try {
      ToastManager.success('职级创建成功');
      widget.onSuccess?.call();
      resetFormData();

      if (!isAddNext && mounted) {
        Navigator.of(context).pop();
      }
    } catch (error) {
      ToastManager.error('创建失败: $error');
      widget.onError?.call();
    } finally {
      if (mounted) {
        setState(() {
          btnLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppDialog(
      width: 480,
      title: '添加职级',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Checkbox(
            value: isAddNext,
            onChanged: (value) {
              setState(() {
                isAddNext = value ?? false;
              });
            },
          ),
          const Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(
            text: '确定',
            type: ButtonType.primary,
            loading: btnLoading,
            onPressed: () => createRequest(context),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppInput(
              label: "职级名称",
              labelWidth: 80,
              labelPosition: LabelPosition.left,
              hintText: "请输入职级名称",
              size: InputSize.medium,
              controller: _nameController,
              maxLength: 30,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入职级名称';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
}
